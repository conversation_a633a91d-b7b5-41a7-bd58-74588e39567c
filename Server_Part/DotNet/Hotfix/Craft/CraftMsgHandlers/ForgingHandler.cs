using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ForgingHandler : MessageLocationHandler<MapNode, ForgingReq, ForgingResp>
  {
    protected override async ETTask Run(MapNode nowMap, ForgingReq request, ForgingResp response)
    {
      // 获取用户并验证
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }

      // 获取锻造配方
      if (!GlobalInfoCache.Instance.allForgingRecipeCache.TryGetValue(request.recipeId, out ForgingRecipe recipe))
      {
        response.SetError("锻造配方不存在");
        return;
      }

      // 获取用户背包
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (bagComponent == null)
      {
        response.SetError("背包组件不存在");
        return;
      }

      // 检查材料是否足够
      LogicRet materialCheckRet = CheckMaterials(bagComponent, recipe.needThings);
      if (!materialCheckRet.IsSuccess)
      {
        response.SetError(materialCheckRet.Message);
        return;
      }

      // 扣除材料
      LogicRet consumeRet = ConsumeMaterials(bagComponent, recipe.needThings);
      if (!consumeRet.IsSuccess)
      {
        response.SetError(consumeRet.Message);
        return;
      }

      // 给予产出物品
      if (recipe.giveThings != null && recipe.giveThings.Count > 0)
      {
        bagComponent.GiveThing(recipe.giveThings, ThingFromType.Forging);
      }

      // 发送成功消息给用户
      user.SendToast($"锻造 {recipe.name} 成功！");

      await ETTask.CompletedTask;
    }

    /// <summary>
    /// 检查材料是否足够
    /// </summary>
    private static LogicRet CheckMaterials(BagComponent bagComponent, List<ThingGiveInfo> needThings)
    {
      if (needThings == null || needThings.Count == 0)
      {
        return LogicRet.Success;
      }

      foreach (ThingGiveInfo needThing in needThings)
      {
        int totalNum = bagComponent.GetThingTotalNum(needThing.thingName, ThingGrade.None, needThing.ownType);
        if (totalNum < needThing.num)
        {
          return LogicRet.Failed($"材料 {needThing.thingName} 不足，需要 {needThing.num} 个，当前拥有 {totalNum} 个");
        }
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 扣除材料
    /// </summary>
    private static LogicRet ConsumeMaterials(BagComponent bagComponent, List<ThingGiveInfo> needThings)
    {
      if (needThings == null || needThings.Count == 0)
      {
        return LogicRet.Success;
      }

      try
      {
        foreach (ThingGiveInfo needThing in needThings)
        {
          // 获取背包中的物品
          List<Thing> things = bagComponent.GetThingInBag(needThing.thingName, needThing.ownType);
          if (things == null || things.Count == 0)
          {
            return LogicRet.Failed($"背包中没有找到材料 {needThing.thingName}");
          }

          int remainingNeed = needThing.num;
          List<Thing> thingsToUpdate = new List<Thing>();

          // 按数量扣除物品
          foreach (Thing thing in things)
          {
            if (remainingNeed <= 0) break;

            if (thing.num >= remainingNeed)
            {
              thing.num -= remainingNeed;
              remainingNeed = 0;
              thingsToUpdate.Add(thing);
            }
            else
            {
              remainingNeed -= thing.num;
              thing.num = 0;
              thingsToUpdate.Add(thing);
            }
          }

          if (remainingNeed > 0)
          {
            return LogicRet.Failed($"材料 {needThing.thingName} 数量不足");
          }

          // 更新背包
          foreach (Thing thing in thingsToUpdate)
          {
            if (thing.num <= 0)
            {
              bagComponent.RemoveThing(thing);
            }
          }
        }

        return LogicRet.Success;
      }
      catch (System.Exception ex)
      {
        ETLog.Error($"扣除材料时发生错误: {ex.Message}");
        return LogicRet.Failed("扣除材料时发生内部错误");
      }
    }
  }
}
